package com.saigeware.sh.saiwell;

import android.annotation.SuppressLint;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.jstyle.blesdk2301.Util.BleSDK;
import com.jstyle.blesdk2301.callback.BleConnectionListener;
import com.jstyle.blesdk2301.callback.DataListener2301;
import com.jstyle.blesdk2301.callback.OnScanResults;
import com.jstyle.blesdk2301.model.Device;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import androidx.annotation.Nullable;

public final class BleService extends Service {
    private static final String TAG = "BleService";
    private static final UUID NOTIY = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");
    private static final UUID SERVICE_DATA = UUID.fromString("0000fff0-0000-1000-8000-00805f9b34fb");
    private static final UUID DATA_Characteristic = UUID.fromString("0000fff6-0000-1000-8000-00805f9b34fb");
    private static final UUID NOTIY_Characteristic = UUID.fromString("0000fff7-0000-1000-8000-00805f9b34fb");
    
    private boolean needReconnect = false;
    private boolean fastconnect = false;
    private final IBinder binder = new LocalBinder();
    private BluetoothManager bluetoothManager;
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothGatt bluetoothGatt;
    private boolean isConnected;
    private Handler handler = new Handler(Looper.getMainLooper());
    private String address;
    private Context context;
    private BleConnectionListener bleConnectionListener;
    private static BleService instance;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // Priority connection timeout handling
    private Runnable connectionTimeoutRunnable;
    private static final long PRIORITY_CONNECTION_TIMEOUT = 10000; // 10 seconds for priority connections
    private static final long NORMAL_CONNECTION_TIMEOUT = 15000; // 15 seconds for normal connections

    public BleService() {
        // Required empty constructor for Service
    }



    @Override
    public void onCreate() {
        super.onCreate();
        context = this;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        initAdapter();
        return binder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    /**
     * Initialize Bluetooth device
     */
    public void initBluetoothDevice(final String address, final Context context, boolean needReconnect, BleConnectionListener connectionListener) {
        initBluetoothDeviceWithPriority(address, context, needReconnect, connectionListener);
    }

    /**
     * Initialize Bluetooth device with priority optimizations for fast connection
     */
    public void initBluetoothDeviceWithPriority(final String address, final Context context, boolean needReconnect, BleConnectionListener connectionListener) {
        if (connectionListener != null) {
            this.bleConnectionListener = connectionListener;
            this.needReconnect = needReconnect;
        }

        Log.d(TAG, "====== RING DEBUG ====== Starting PRIORITY connection to: " + address);
        fastconnect = true; // Enable fast connect mode
        this.address = address;
        this.context = context;

        final BluetoothDevice device = bluetoothAdapter.getRemoteDevice(address);
        if (isConnected()) {
            Log.d(TAG, "====== RING DEBUG ====== Already connected to device - skipping connection");
            if (connectionListener != null) {
                connectionListener.ConnectionSucceeded();
            }
            return;
        }

        // Priority optimization: Check if device is bonded for faster connection
        boolean isBonded = device.getBondState() == BluetoothDevice.BOND_BONDED;
        Log.d(TAG, "====== RING DEBUG ====== Device bonded status: " + isBonded + " for address: " + address);

        // Priority optimization: Force close any existing GATT connection immediately
        if (bluetoothGatt != null) {
            Log.d(TAG, "====== RING DEBUG ====== Force closing existing GATT connection for priority");
            try {
                // Refresh cache before disconnecting for better cleanup
                refreshDeviceCache(bluetoothGatt);
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
            } catch (Exception e) {
                Log.w(TAG, "====== RING DEBUG ====== Exception during GATT cleanup: " + e.getMessage());
            }
            bluetoothGatt = null;

            // Brief pause to ensure clean state - reduced for priority connections
            try {
                Thread.sleep(fastconnect ? 25 : 50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        Log.d(TAG, "====== RING DEBUG ====== Initiating PRIORITY GATT connection to device");

        // Priority optimization: Use autoConnect based on bonded status and priority
        // For bonded devices, autoConnect=true can be faster for subsequent connections
        // For unbonded devices or first-time connections, autoConnect=false is faster
        boolean useAutoConnect = isBonded && !fastconnect; // Use autoConnect only for bonded non-priority connections

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Log.d(TAG, "====== RING DEBUG ====== Using PRIORITY transport type LE for connection (API 23+), autoConnect=" + useAutoConnect);
            bluetoothGatt = device.connectGatt(context, useAutoConnect, bleGattCallback, BluetoothDevice.TRANSPORT_LE);
        } else {
            Log.d(TAG, "====== RING DEBUG ====== Using PRIORITY legacy connection method, autoConnect=" + useAutoConnect);
            bluetoothGatt = device.connectGatt(context, useAutoConnect, bleGattCallback);
        }

        if (bluetoothGatt == null) {
            Log.e(TAG, "====== RING DEBUG ====== Failed to create PRIORITY GATT connection for device: " + address);
            if (connectionListener != null) {
                connectionListener.ConnectionFailed();
            }
            return;
        }

        // Priority optimization: Request high priority connection parameters
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                boolean prioritySet = bluetoothGatt.requestConnectionPriority(BluetoothGatt.CONNECTION_PRIORITY_HIGH);
                Log.d(TAG, "====== RING DEBUG ====== High priority connection requested: " + prioritySet);
            } catch (Exception e) {
                Log.w(TAG, "====== RING DEBUG ====== Failed to set connection priority: " + e.getMessage());
            }
        }

        if (bleConnectionListener != null) {
            Log.d(TAG, "====== RING DEBUG ====== Notifying that PRIORITY connection is in progress");
            bleConnectionListener.Connecting();
        }

        // Priority optimization: Set connection timeout
        setupConnectionTimeout(fastconnect);
    }

    /**
     * Setup connection timeout based on priority
     */
    private void setupConnectionTimeout(boolean isPriority) {
        // Cancel any existing timeout
        if (connectionTimeoutRunnable != null) {
            handler.removeCallbacks(connectionTimeoutRunnable);
        }

        long timeout = isPriority ? PRIORITY_CONNECTION_TIMEOUT : NORMAL_CONNECTION_TIMEOUT;
        String timeoutType = isPriority ? "PRIORITY" : "NORMAL";

        connectionTimeoutRunnable = () -> {
            Log.w(TAG, "====== RING DEBUG ====== " + timeoutType + " connection timeout reached");
            if (!isConnected && bluetoothGatt != null) {
                Log.d(TAG, "====== RING DEBUG ====== Forcing disconnection due to timeout");
                try {
                    bluetoothGatt.disconnect();
                    bluetoothGatt.close();
                } catch (Exception e) {
                    Log.w(TAG, "====== RING DEBUG ====== Exception during timeout cleanup: " + e.getMessage());
                }
                bluetoothGatt = null;

                if (bleConnectionListener != null) {
                    handler.post(() -> {
                        Log.d(TAG, "====== RING DEBUG ====== Notifying connection timeout failure");
                        bleConnectionListener.ConnectionFailed();
                    });
                }
            }
        };

        handler.postDelayed(connectionTimeoutRunnable, timeout);
        Log.d(TAG, "====== RING DEBUG ====== " + timeoutType + " connection timeout set for " + timeout + "ms");
    }

    /**
     * Cancel connection timeout
     */
    private void cancelConnectionTimeout() {
        if (connectionTimeoutRunnable != null) {
            handler.removeCallbacks(connectionTimeoutRunnable);
            connectionTimeoutRunnable = null;
            Log.d(TAG, "====== RING DEBUG ====== Connection timeout cancelled");
        }
    }

    /**
     * Initialize Bluetooth adapter
     */
    private void initAdapter() {
        if (bluetoothManager == null) {
            bluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager == null) {
                return;
            }
        }
        bluetoothAdapter = bluetoothManager.getAdapter();
    }

    /**
     * Disconnect from device
     */
    public void disconnect() {
        if (bluetoothAdapter == null || bluetoothGatt == null) {
            Log.d(TAG, "====== RING DEBUG ====== Cannot disconnect - BT adapter or GATT is null");
            return;
        }
        Log.d(TAG, "====== RING DEBUG ====== Disconnecting from GATT server");
        bluetoothGatt.disconnect();
    }

    /**
     * Service binder class
     */
    public class LocalBinder extends Binder {
        public BleService getService() {
            return BleService.this;
        }
    }

    /**
     * Bluetooth GATT callback
     */
    private BluetoothGattCallback bleGattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            String priorityText = fastconnect ? "PRIORITY " : "";
            Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Connection state changed: status=" + status + ", newState=" + newState);

            if (newState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Connected to GATT server.");
                isConnected = true;

                // Cancel connection timeout since we're now connected
                cancelConnectionTimeout();

                // Priority optimization: Set connection priority immediately after connection
                if (fastconnect && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    try {
                        boolean prioritySet = bluetoothGatt.requestConnectionPriority(BluetoothGatt.CONNECTION_PRIORITY_HIGH);
                        Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Post-connection priority set: " + prioritySet);
                    } catch (Exception e) {
                        Log.w(TAG, "====== RING DEBUG ====== Failed to set post-connection priority: " + e.getMessage());
                    }
                }

                // Discover services after successful connection
                if (bluetoothGatt != null) {
                    Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Starting service discovery");

                    // Priority optimization: Reduce delay before service discovery for fast connect
                    if (fastconnect) {
                        bluetoothGatt.discoverServices();
                    } else {
                        // Small delay for non-priority connections to ensure stability
                        handler.postDelayed(() -> {
                            if (bluetoothGatt != null) {
                                bluetoothGatt.discoverServices();
                            }
                        }, 100);
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== BluetoothGatt is null after connection");
                }

                if (bleConnectionListener != null) {
                    handler.post(() -> {
                        Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Notifying connection succeeded");
                        bleConnectionListener.BleStatus(status, newState);
                        bleConnectionListener.ConnectionSucceeded();
                    });
                } else {
                    Log.w(TAG, "====== RING DEBUG ====== No connection listener to notify of success");
                }
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Disconnected from GATT server.");
                isConnected = false;

                // Cancel connection timeout since connection attempt is over
                cancelConnectionTimeout();

                if (bleConnectionListener != null) {
                    handler.post(() -> {
                        Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Notifying connection failed/disconnected");
                        bleConnectionListener.BleStatus(status, newState);
                        bleConnectionListener.ConnectionFailed();
                    });
                } else {
                    Log.w(TAG, "====== RING DEBUG ====== No connection listener to notify of disconnect");
                }

                // Reconnect if needed with optimized timing
                if (needReconnect && bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
                    Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Attempting reconnection due to needReconnect flag");

                    if (bleConnectionListener != null) {
                        handler.post(() -> {
                            Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Notifying that reconnection is in progress");
                            bleConnectionListener.Connecting();
                        });
                    }

                    // Priority optimization: Faster reconnection for priority connections
                    long reconnectDelay = fastconnect ? 1000 : 2000; // 1s for priority, 2s for normal
                    handler.postDelayed(() -> {
                        if (address != null && context != null) {
                            if (fastconnect) {
                                initBluetoothDeviceWithPriority(address, context, needReconnect, bleConnectionListener);
                            } else {
                                initBluetoothDevice(address, context, needReconnect, bleConnectionListener);
                            }
                        }
                    }, reconnectDelay);
                }

            }
        }

        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            String priorityText = fastconnect ? "PRIORITY " : "";
            Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Services discovered, status: " + status);

            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Services discovered successfully");
                BluetoothGattService service = gatt.getService(SERVICE_DATA);
                if (service != null) {
                    Log.d(TAG, "====== RING DEBUG ====== " + priorityText + "Found required service: " + SERVICE_DATA);

                    // Priority optimization: Immediate notification setup for fast connect
                    if (fastconnect) {
                        setCharacteristicNotification(true);
                    } else {
                        // Small delay for non-priority connections to ensure stability
                        handler.postDelayed(() -> setCharacteristicNotification(true), 50);
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== " + priorityText + "Required service not found: " + SERVICE_DATA);
                    if (bleConnectionListener != null) {
                        handler.post(() -> bleConnectionListener.ConnectionFailed());
                    }
                }
            } else {
                Log.e(TAG, "====== RING DEBUG ====== " + priorityText + "Service discovery failed with status: " + status);
                if (bleConnectionListener != null) {
                    handler.post(() -> bleConnectionListener.ConnectionFailed());
                }
            }
        }

        @Override
        public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            Log.d(TAG, "====== RING DEBUG ====== Characteristic read, status: " + status);
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "====== RING DEBUG ====== Read value: " + bytesToHex(characteristic.getValue()));
            } else {
                Log.e(TAG, "====== RING DEBUG ====== Characteristic read failed with status: " + status);
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            Log.d(TAG, "====== RING DEBUG ====== Characteristic write, status: " + status);
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "====== RING DEBUG ====== Write succeeded for value: " + bytesToHex(characteristic.getValue()));
                nextQueue();
            } else {
                Log.e(TAG, "====== RING DEBUG ====== Characteristic write failed with status: " + status);
                // Try again if it failed
                isQueueBusy = false;
                nextQueue();
            }
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            byte[] data = characteristic.getValue();
            Log.d(TAG, "====== RING DEBUG ====== Received notification data: " + bytesToHex(data) + " (length: " + data.length + ")");
            // Parse the data through BleSDK if context is a DataListener2301
            if (context instanceof DataListener2301) {
                try {
                    DataListener2301 dataListener = (DataListener2301) context;
                    BleSDK.DataParsingWithData(data, dataListener);
                    Log.d(TAG, "====== RING DEBUG ====== Data passed to BleSDK.DataParsingWithData");
                } catch (Exception e) {
                    Log.e(TAG, "====== RING DEBUG ====== Error parsing data: " + e.getMessage());
                }
            } else {
                Log.w(TAG, "====== RING DEBUG ====== Context is not a DataListener2301 - cannot parse data");
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            Log.d(TAG, "====== RING DEBUG ====== Descriptor write, status: " + status);
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "====== RING DEBUG ====== Notification setup complete");
            } else {
                Log.e(TAG, "====== RING DEBUG ====== Descriptor write failed with status: " + status);
            }
        }
    };

    /**
     * Helper method to convert bytes to hex string
     */
    private String bytesToHex(byte[] bytes) {
        if (bytes == null) return "";
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    /**
     * Refresh device cache for better connection reliability
     */
    public boolean refreshDeviceCache(BluetoothGatt gatt) {
        try {
            if (gatt == null) {
                Log.w(TAG, "====== RING DEBUG ====== Cannot refresh cache: GATT is null");
                return false;
            }

            Method localMethod = gatt.getClass().getMethod("refresh");
            if (localMethod != null) {
                boolean result = (Boolean) localMethod.invoke(gatt);
                Log.d(TAG, "====== RING DEBUG ====== Device cache refresh result: " + result);
                return result;
            }
        } catch (Exception e) {
            Log.e(TAG, "====== RING DEBUG ====== Exception occurred while refreshing device cache: " + e.getMessage());
        }
        return false;
    }

    /**
     * Set characteristic notification
     * 
     * @param enable Whether to enable or disable notification
     */
    public void setCharacteristicNotification(boolean enable) {
        if (bluetoothAdapter == null || bluetoothGatt == null) {
            Log.w(TAG, "BluetoothAdapter not initialized or GATT server was disconnected");
            return;
        }
        
        BluetoothGattService gattService = bluetoothGatt.getService(SERVICE_DATA);
        if (gattService == null) {
            Log.e(TAG, "Service not found");
            return;
        }
        
        BluetoothGattCharacteristic characteristic = gattService.getCharacteristic(NOTIY_Characteristic);
        if (characteristic == null) {
            Log.e(TAG, "Characteristic not found");
            return;
        }
        
        boolean success = bluetoothGatt.setCharacteristicNotification(characteristic, enable);
        if (!success) {
            Log.e(TAG, "Failed to set characteristic notification");
            return;
        }
        
        BluetoothGattDescriptor descriptor = characteristic.getDescriptor(NOTIY);
        if (descriptor == null) {
            Log.e(TAG, "Descriptor not found");
            return;
        }
        
        descriptor.setValue(enable ? BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE : BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE);
        boolean writeSuccess = bluetoothGatt.writeDescriptor(descriptor);
        if (!writeSuccess) {
            Log.e(TAG, "Failed to write descriptor");
        }
    }

    /**
     * Write value to characteristic
     * 
     * @param value Value to write
     */
    public void writeValue(final byte[] value) {
        if (!isConnected) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot write - not connected");
            return;
        }
        
        if (bluetoothGatt == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot write - GATT is null");
            return;
        }

        Log.d(TAG, "====== RING DEBUG ====== Writing value: " + bytesToHex(value));
        // Get the service and characteristic
        BluetoothGattService service = bluetoothGatt.getService(SERVICE_DATA);
        if (service == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot write - service not found");
            return;
        }

        BluetoothGattCharacteristic characteristic = service.getCharacteristic(DATA_Characteristic);
        if (characteristic == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot write - characteristic not found");
            return;
        }

        // Set the value and write to the device
        characteristic.setValue(value);
        boolean writeStarted = bluetoothGatt.writeCharacteristic(characteristic);
        if (writeStarted) {
            Log.d(TAG, "====== RING DEBUG ====== Write operation started successfully");
        } else {
            Log.e(TAG, "====== RING DEBUG ====== Failed to start write operation");
        }
    }

    // Command queue for sending multiple commands
    private Queue<byte[]> commandQueue = new LinkedList<>();
    private boolean isQueueBusy = false;


    public void nextQueue() {
        if (commandQueue.isEmpty()) {
            isQueueBusy = false;
            return;
        }
        
        isQueueBusy = true;
        byte[] value = commandQueue.poll();
        writeValue(value);
    }

    /**
     * Check if device is connected
     *
     * @return true if connected, false otherwise
     */
    public boolean isConnected() {
        return isConnected;
    }

    /**
     * Pre-warm BLE connection for faster subsequent connections
     * This method prepares the BLE stack for faster connections
     */
    public void preWarmConnection(String deviceAddress) {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            Log.w(TAG, "====== RING DEBUG ====== Cannot pre-warm: Bluetooth not available");
            return;
        }

        try {
            BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
            if (device != null) {
                Log.d(TAG, "====== RING DEBUG ====== Pre-warming connection for device: " + deviceAddress);
                // This helps the BLE stack prepare for faster connection
                device.getBondState(); // Triggers internal cache preparation

                // For bonded devices, we can also trigger a brief connection attempt
                if (device.getBondState() == BluetoothDevice.BOND_BONDED) {
                    Log.d(TAG, "====== RING DEBUG ====== Device is bonded, pre-warming complete");
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "====== RING DEBUG ====== Exception during pre-warm: " + e.getMessage());
        }
    }



    @Override
    public void onDestroy() {
        super.onDestroy();
        if (bluetoothGatt != null) {
            bluetoothGatt.close();
            bluetoothGatt = null;
        }
    }
} 



