# Firebase Remote Config Keys Documentation

This document provides a comprehensive list of all Firebase Remote Config keys used in the SAiWELL Flutter application with their one-line explanations.

## Version Control Keys

| Key | Environment | Description |
|-----|-------------|-------------|
| `MIN_VERSION` | Global | Minimum app version required to use the application |
| `ANDROID_MIN_VERSION` | Global | Minimum Android app version required for Android devices |

## Health Data Configuration

| Key | Environment | Description |
|-----|-------------|-------------|
| `APPLE_HEALTH_TYPES_{ENV}` | Staging/Production | JSON configuration for Apple HealthKit data types to sync |
| `ANDROID_HEALTH_TYPES_{ENV}` | Staging/Production | JSON configuration for Android Health Connect data types to sync |

## API Rate Limiting

| Key | Environment | Description |
|-----|-------------|-------------|
| `AMBEE_BEFORE_NOON_ALLOWED_COUNT_{ENV}` | Staging/Production | Maximum allowed Ambee API calls before noon |
| `AMBEE_AFTER_NOON_ALLOWED_COUNT_{ENV}` | Staging/Production | Maximum allowed Ambee API calls after noon |

## Voice Recording Configuration

| Key | Environment | Description |
|-----|-------------|-------------|
| `VOICE_CONTENT` | Global | JSON configuration for voice recording content prompts |
| `VOICE_INSTRUCTIONS` | Global | JSON array of instructions displayed before voice recording |

## PPG (Heart Rate) Configuration

| Key | Environment | Description |
|-----|-------------|-------------|
| `PPG_INSTRUCTIONS` | Global | JSON array of instructions displayed before PPG recording |
| `PPG_RECORDING_DURATION_SECONDS` | Production | Duration in seconds for PPG recording in production environment |
| `PPG_RECORDING_DURATION_SECONDS_STAGING` | Staging | Duration in seconds for PPG recording in staging environment |

## Navigation & URL Configuration

| Key | Environment | Description |
|-----|-------------|-------------|
| `NAVIGATION_PRODUCTION` | Production | JSON configuration for app navigation routes in production |
| `NAVIGATION_STAGING` | Staging | JSON configuration for app navigation routes in staging |
| `BaseUrlDev` | Development | Base URL for API endpoints in development environment |
| `BaseUrlProd` | Production | Base URL for API endpoints in production environment |

## Instruction Configurations

| Key | Environment | Description |
|-----|-------------|-------------|
| `INSTRUCTION_CONFIGS` | Global | JSON configuration for camera capture instructions by type |

## Auto Monitoring Configuration

| Key | Environment | Description |
|-----|-------------|-------------|
| `AUTO_MONITORING_V2` | Global | JSON configuration for heart rate monitoring schedules and settings |

## Notification Schedules

| Key | Environment | Description |
|-----|-------------|-------------|
| `RING_NOTIFICATION_SCHEDULE_PROD` | Production | JSON array of scheduled ring check notifications for production |
| `RING_NOTIFICATION_SCHEDULE_DEV` | Development | JSON array of scheduled ring check notifications for development |
| `VOICE_NOTIFICATION_SCHEDULE_PROD` | Production | JSON array of scheduled voice recording notifications for production |
| `VOICE_NOTIFICATION_SCHEDULE_DEV` | Development | JSON array of scheduled voice recording notifications for development |
| `VIALS_NOTIFICATION_SCHEDULE_PROD` | Production | JSON array of scheduled vials scanning notifications for production |
| `VIALS_NOTIFICATION_SCHEDULE_DEV` | Development | JSON array of scheduled vials scanning notifications for development |

## Notification Exclusions

| Key | Environment | Description |
|-----|-------------|-------------|
| `VOICE_NOTIFICATION_EXCLUSION_LIST_PROD` | Production | JSON list of programs excluded from voice notifications in production |
| `VOICE_NOTIFICATION_EXCLUSION_LIST_DEV` | Development | JSON list of programs excluded from voice notifications in development |

## Default Values

The following default values are set when Remote Config fails to load:

- **MIN_VERSION**: `"0.0.1"`
- **ANDROID_MIN_VERSION**: `"0.0.1"`
- **Health Data Types**: Empty arrays for both platforms
- **Ambee API Counts**: `0` for both before and after noon
- **Voice Content**: Empty content string
- **Voice Instructions**: Empty instructions array
- **Recording Duration**: `60` seconds for both environments
- **Notification Schedules**: Default single notifications at 8 AM (ring), 12 PM (voice), and 10 AM (vials)
- **Exclusion Lists**: Empty excluded programs array
- **Navigation Config**: Empty navigation array with empty base URL
- **Base URLs**: Empty strings

## Environment Suffix

Keys marked with `{ENV}` are automatically suffixed with:
- `_STAGING` for staging environment
- `_PRODUCTION` for production environment

This allows different configurations for different deployment environments.

## Usage Notes

1. All JSON configurations should be valid JSON strings
2. Notification schedules use 24-hour format for time
3. Health data types are platform-specific (Apple vs Android)
4. API rate limits are reset daily
5. Recording durations are in seconds
6. Environment-specific keys are automatically selected based on build configuration
