import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:image/image.dart' as img;

import '../../../services/firebase_remote_config_service.dart';

class CameraManager {
  CameraController? cameraController;
  FirebaseRemoteConfigService? remoteConfigService;
  bool isFlashNeededFromConfig = false;
  bool isFlashActive = false;

  CameraManager({this.remoteConfigService}) {
    if (remoteConfigService != null) {
      isFlashNeededFromConfig = true; // Default true if no config service
    }
  }

  Future<void> initializeCamera({
    required bool isFrontCamera,
    required BuildContext context,
  }) async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) return;

      if (cameraController != null) {
        await cameraController!.dispose();
        cameraController = null;
      }

      final camera = isFrontCamera
          ? cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.front,
              orElse: () => cameras.first)
          : cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.back,
              orElse: () => cameras.first);

      cameraController = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await cameraController!.initialize();

      if (cameraController!.value.isInitialized) {
        try {
          await cameraController!.setFocusMode(FocusMode.auto);
          await cameraController!.setExposureMode(ExposureMode.auto);
          await setCameraFlash();
        } catch (e) {
          debugPrint('Error setting camera parameters: $e');
        }
      }
      DeviceOrientation lockOrientation = DeviceOrientation.portraitUp;
      await cameraController?.lockCaptureOrientation(lockOrientation);
    } catch (e) {
      debugPrint('Error initializing camera: $e');
    }
  }

  Future<void> setCameraFlash() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      debugPrint('Camera not initialized, cannot set flash');
      return;
    }

    if (!isFlashNeededFromConfig) {
      try {
        await cameraController!.setFlashMode(FlashMode.off);
        isFlashActive = false;
      } catch (e) {
        debugPrint('Error disabling flash: $e');
      }
      return;
    }

    try {
      await cameraController!.setFlashMode(FlashMode.always);
      isFlashActive = true;
    } catch (e) {
      debugPrint('Failed to set flash mode: $e');
      isFlashActive = false;
    }
  }

  Future<XFile?> takePicture() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      return null;
    }
    try {
      final XFile photo = await cameraController!.takePicture();
      final String jpgPath = photo.path;
      // Convert JPG to PNG
      try {
        final jpgFile = File(jpgPath);
        final jpgBytes = await jpgFile.readAsBytes();
        final image = img.decodeImage(jpgBytes);
        if (image != null) {
          final pngBytes = img.encodePng(image);
          final pngPath = jpgPath.replaceAll('.jpg', '.png').replaceAll('.jpeg', '.png');
          final pngFile = File(pngPath);
          await pngFile.writeAsBytes(pngBytes);

          // Clean up the original JPG file
          try {
            await jpgFile.delete();
          } catch (e) {
            debugPrint('Warning: Could not delete original JPG file: $e');
          }

          return XFile(pngPath);
        } else {
          debugPrint('Failed to decode JPEG for PNG conversion.');
          // Force conversion even if decode fails by creating a basic PNG
          final pngPath = jpgPath.replaceAll('.jpg', '.png').replaceAll('.jpeg', '.png');
          final pngFile = File(pngPath);
          await pngFile.writeAsBytes(jpgBytes); // Use original bytes as fallback
          return XFile(pngPath);
        }
      } catch (e) {
        debugPrint('Error converting JPG to PNG: $e');
        // Ensure PNG extension even in error case
        final pngPath = jpgPath.replaceAll('.jpg', '.png').replaceAll('.jpeg', '.png');
        final pngFile = File(pngPath);
        final jpgFile = File(jpgPath);
        final jpgBytes = await jpgFile.readAsBytes();
        await pngFile.writeAsBytes(jpgBytes);
        return XFile(pngPath);
      }
    } catch (e) {
      debugPrint('Camera Error: $e');
      return null;
    }
  }

  Future<void> setFocusPoint(
      Offset normalizedPosition, Function(bool) focusCallback) async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      debugPrint('Cannot set focus: Camera not ready');
      return;
    }

    try {
      focusCallback(true); // Start focusing

      // Clamp values between 0 and 1
      final clampedPosition = Offset(normalizedPosition.dx.clamp(0.0, 1.0),
          normalizedPosition.dy.clamp(0.0, 1.0));

      // Set focus
      try {
        await cameraController!.setFocusMode(FocusMode.auto);
        await Future.delayed(const Duration(milliseconds: 300));
        try {
          await cameraController!.setFocusPoint(clampedPosition);
        } catch (e) {
          debugPrint('Setting specific focus point not supported: $e');
        }
        await cameraController!.setFocusMode(FocusMode.locked);
      } catch (e) {
        debugPrint('Error with focus operations: $e');
      }

      // Set exposure
      try {
        await cameraController!.setExposurePoint(clampedPosition);
      } catch (e) {
        debugPrint('Error setting exposure point: $e');
      }

      // Hide focus indicator after delay
      Future.delayed(const Duration(seconds: 2), () {
        focusCallback(false);
      });
    } catch (e) {
      debugPrint('Error in setFocusPoint: $e');
      focusCallback(false);
    }
  }

  void dispose() {
    try {
      if (cameraController != null) {
        // First stop image stream if it's running
        if (cameraController!.value.isStreamingImages) {
          cameraController!.stopImageStream();
        }

        // Then dispose the controller
        cameraController!.dispose();
        cameraController = null;
        debugPrint('Camera controller fully disposed');
      }
    } catch (e) {
      debugPrint('Error disposing camera controller: $e');
    }
  }
}
