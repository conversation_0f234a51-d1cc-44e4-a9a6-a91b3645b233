import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'package:SAiWELL/models/ble_device.dart';
import 'package:SAiWELL/models/health_datatype.dart';
import 'package:SAiWELL/models/hrv_reading_model_v2.dart';
import 'package:SAiWELL/models/oxygen_reading_model_v2.dart';
import 'package:SAiWELL/models/sleep_reading_model_v2.dart';
import 'package:SAiWELL/models/steps_reading_model_v2.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/services/firestore_service.dart';
import 'package:SAiWELL/services/health_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../common_controllers/global_controller.dart';
import '../constants/constant.dart';
import '../models/hr_monitoring_remote_config_model.dart';
import '../models/temperature_reading_model_v2.dart';

class NativeCommunicator {
  static late final MethodChannel _platform;
  static final NativeCommunicator _instance = NativeCommunicator._internal();

  factory NativeCommunicator() => _instance;

  final FirestoreService _firestoreService;
  final PrefsService _prefsService;
  GlobalController globalController = Get.find<GlobalController>();
  final FirebaseRemoteConfigService _firebaseRemoteConfigService;
  final HealthService _healthService;
  Map<String, Timestamp> updatedFields = {};

  final _operationQueue = Queue<Future<dynamic> Function()>();
  bool _isProcessing = false;
  final Map<String, bool> _operationStatus = {};

  // Lock mechanism using Completer
  Completer<void> _lock = Completer<void>()..complete();

  final StreamController<Map<dynamic, dynamic>> _healthDataController =
      StreamController.broadcast();
  Stream<Map<dynamic, dynamic>> get healthDataStream =>
      _healthDataController.stream;

  Map<String, dynamic> _healthData = {};
  Map<String, dynamic> get healthData => Map<String, dynamic>.from(_healthData);
  bool _isInitialized = false;

  NativeCommunicator._internal()
      : _firestoreService = FirestoreService(),
        _prefsService = PrefsService(),
        _firebaseRemoteConfigService = FirebaseRemoteConfigService(),
        _healthService = HealthService() {
    _platform = MethodChannel(Platform.isIOS
        ? 'com.saiwell.sw/ios_native'
        : 'com.saiwell.sw/android_native');
    _platform.setMethodCallHandler(_handleMethodCall);
  }

  Future<void> _handleMethodCall(MethodCall call) async {
    try {
      switch (call.method) {
        case "onHealthDataChange":
          _healthDataController.add(call.arguments);
          break;
        case "onConnectionStateChange":
          _handleConnectionStateChange(call.arguments);
          break;
        case "BluetoothSwitchIsTurnedOff":
          _handleBluetoothTurnedOff();
          break;
        default:
          // Keep error log for unhandled method calls
          print('Unhandled method call: ${call.method}');
      }
    } catch (e, stack) {
      // Keep error logs for method call handling
      print('Error handling method call ${call.method}: $e');
      print('Stack trace: $stack');
    }
  }

  void _handleConnectionStateChange(dynamic arguments) {
    try {
      if (arguments is Map) {
        final bool isConnected = arguments['isConnected'] ?? false;
        globalController.isConnected.value = isConnected;

        if (isConnected) {
          globalController.currentToastStatus.value =
              CurrentToastStatus.connected;
        } else {
          globalController.currentToastStatus.value =
              CurrentToastStatus.notConnected;
        }
      }
    } catch (e) {
      // Keep error log for connection state change handling
      print('Error handling connection state change: $e');
    }
  }

  void _handleBluetoothTurnedOff() {
    try {
      globalController.isConnected.value = false;
      globalController.currentToastStatus.value =
          CurrentToastStatus.notConnected;
    } catch (e) {
      // Keep error log for Bluetooth turned off handling
      print('Error handling Bluetooth turned off: $e');
    }
  }

  Future<T> _enqueue<T>(Future<T> Function() operation) async {
    final completer = Completer<T>();

    // Wait for the current lock to be released before adding the new operation
    await _lock.future;

    // Acquire the lock
    _lock = Completer<void>();

    _operationQueue.add(() async {
      try {
        final result = await operation();
        completer.complete(result);
        return result;
      } catch (e) {
        completer.completeError(e);
        rethrow;
      } finally {
        // Release the lock when the operation completes
        if (!_lock.isCompleted) {
          _lock.complete();
        }
      }
    });

    if (!_isProcessing) {
      _processQueue();
    }

    return completer.future;
  }

  void _processQueue() async {
    _isProcessing = true;
    while (_operationQueue.isNotEmpty) {
      final operation = _operationQueue.removeFirst();
      try {
        await operation();
      } catch (e) {
        // Keep error log for queue operations
        print('Queue operation error: $e');
      }
    }
    _isProcessing = false;
  }

  Future<List<BLEDevice>> startScanDevicesV2() {
    return _enqueue(() async {
      List<BLEDevice> devices = [];
      try {
        final result = await _platform.invokeMethod('startScanDevicesV2');
        if (result is List) {
          for (var item in result) {
            if (item is Map) {
              String? name = item['name'];
              String? macId = item['macId'];
              if (name != null && macId != null && name.contains('2301')) {
                devices.add(BLEDevice(name: name, macId: macId));
              }
            }
          }
        }
        return devices;
      } on PlatformException catch (e) {
        // Keep error log for native method calls
        print('Failed to call native methodV2: ${e.message}');
        return devices;
      }
    });
  }

  Future<void> connectToDeviceV2({
    required String macId,
    required void Function()? updateRingStatusToWebView,
    required void Function() reloadWebView,
    required void Function() updateDeviceConnectionDetailsToWebview,
    required void Function() updateBetteryStatusInWebView,
  }) {
    return _enqueue(() async {
      try {
        await Future.delayed(const Duration(milliseconds: 100), (){});
        final result = await _platform.invokeMethod('connectToDeviceV2', macId);
        if (result == true) {
          globalController.isConnected.value = result;
          updateDeviceConnectionDetailsToWebview();
          fetchAllData(
            updateRingStatusToWebView: updateRingStatusToWebView,
            reloadWebView: reloadWebView,
            updateBetteryStatusInWebView: updateBetteryStatusInWebView,
          );
        } else {
          debugPrint("Result is false : $result");
        }
      } on PlatformException catch (e) {
        // Keep error log for device connection
        print('Failed to connect to device: ${e.message}');
      }
    });
  }

  Future<void> disconnectToDeviceV2() {
    return _enqueue(() async {
      try {
        await _platform.invokeMethod('disconnectToDeviceV2');
      } on PlatformException catch (e) {
        // Keep error log for device disconnection
        print('Failed to disconnectToDeviceV2 to device: ${e.message}');
      }
    });
  }

  Future<void> fetchAllData({
    required void Function()? updateRingStatusToWebView,
    required void Function() reloadWebView,
    required void Function() updateBetteryStatusInWebView,
  }) async {
    await executeDeviceOperation(
      reloadWebView: reloadWebView,
      updateBetteryStatusInWebView: updateBetteryStatusInWebView,
    );
    bool isRingFirstConnection = await _prefsService.getIsRingFirstConnection();
    if (isRingFirstConnection && updateRingStatusToWebView != null) {
      updateRingStatusToWebView();
    }
    await fetchHealthDataAndUpload();
    // Add a delay before calling reloadWebView to ensure all data is properly synced
    // Reduce delay for Android to improve performance
    if (Platform.isAndroid) {
      await Future.delayed(const Duration(milliseconds: 500));
    } else {
      await Future.delayed(const Duration(seconds: 1));
    }
    reloadWebView();
    globalController.currentToastStatus.value = CurrentToastStatus.completed;
  }

  Future<void> fetchHealthDataAndUpload() async {
    // Reduce initial delay for Android to improve performance
    if (Platform.isAndroid) {
      await Future.delayed(const Duration(milliseconds: 300));
    } else {
      await Future.delayed(const Duration(seconds: 1));
    }
    updatedFields = {};
    await executeHealthOperationsQueue();
    try {
      _firestoreService.addDeviceMetaDataV2(moreData: updatedFields);
    } catch (e) {
      // Keep error log for device metadata
      print('Error in addDeviceMetaDataV2: $e');
    }
  }

  Future<void> executeHealthOperationsQueue() async {
    final operations = [
      Operation('postDateTime', postDateTimeV2),
      Operation(
          'setAutomaticHRMonitoringThree', setAutomaticHRMonitoringThreeV2),
      Operation('setAutomaticHRMonitoringOne', setAutomaticHRMonitoringOneV2),
      Operation('setAutomaticHRMonitoringTwo', setAutomaticHRMonitoringTwoV2),
      Operation('getTotalActivityData', getTotalActivityDataWithModeV2),
      Operation('getTemperatureData', getTemperatureDataV2),
      Operation('getSleepData', getSleepDataV2),
      Operation('getHRVData', getHRVDataV2),
      Operation('getBloodOxygen', getBloodOxygenV2),
    ];

    for (var operation in operations) {
      try {
        // Reduce delay for Android to improve performance
        if (Platform.isAndroid) {
          await Future.delayed(const Duration(milliseconds: 500));
        } else {
          await Future.delayed(const Duration(seconds: 2));
        }
        _operationStatus[operation.name] = false;
        await operation.function();
        _operationStatus[operation.name] = true;
      } catch (e) {
        // Keep error log for operation failures
        print('Error in ${operation.name}: $e');
        _operationStatus[operation.name] = false;
      }
    }
  }

  Future<void> executeDeviceOperation({
    required void Function() reloadWebView,
    required void Function() updateBetteryStatusInWebView,
  }) async {
    final operations = [
      Operation('getBatteryLevelV2', getBatteryLevelV2),
      Operation('getDeviceMac', getDeviceMacV2),
      Operation('getDeviceVersionV2', getDeviceVersionV2),
    ];
    for (var operation in operations) {
      try {
        // Reduce delay for Android to improve performance
        if (Platform.isAndroid) {
          await Future.delayed(const Duration(milliseconds: 300));
        } else {
          await Future.delayed(const Duration(seconds: 2));
        }
        _operationStatus[operation.name] = false;
        await operation.function();
        _operationStatus[operation.name] = true;

        if (operation.name == "getBatteryLevelV2") {
          updateBetteryStatusInWebView();
          // Don't reload the webview here as it will be reloaded after all operations are complete
          globalController.currentToastStatus.value =
              CurrentToastStatus.dataSyncing;
        }
      } catch (e) {
        // Keep error log for operation failures
        print('Error in ${operation.name}: $e');
        _operationStatus[operation.name] = false;
      }
    }
  }

  void modifyUpdatedFields({
    required bool isUpdated,
    required V2CollectionNames collection,
  }) {
    if (isUpdated) {
      int timeStamp = DateTime.now().millisecondsSinceEpoch;
      Timestamp fbTime = Timestamp.fromMillisecondsSinceEpoch(timeStamp);

      updatedFields["${collection.name}LastUpdated"] = fbTime;
    }
  }

  Future<void> deleteUploadedFields({
    required bool isUpdated,
    required V2CollectionNames collection,
  }) async {
    if (isUpdated) {
      Future.delayed(const Duration(seconds: 2), () async {
        if (collection == V2CollectionNames.saiwellRingStepV2) {
          await deleteTotalActivityDataV2();
        } else if (collection == V2CollectionNames.saiwellRingHrvV2) {
          await deleteHRVDataV2();
        } else if (collection == V2CollectionNames.saiwellRingTemperatureV2) {
          await deleteTemperatureDataV2();
        } else if (collection == V2CollectionNames.saiwellRingSleepV2) {
          await deleteSleepDataV2();
        } else if (collection == V2CollectionNames.saiwellRingBloodOxygenV2) {
          await deleteBloodOxygenV2();
        }
      });
    }
  }

  void printOperationStatus() {
    // Operation status reporting removed
  }

  Future<bool> getConnectionStateV2() {
    return _enqueue(() async {
      try {
        if (Platform.isAndroid) {
          final result = await _platform.invokeMethod('getConnectionState');
          return result.toString() == "10";
        } else {
          return await _platform.invokeMethod('isDeviceConnectedV2') as bool;
        }
      } catch (e) {
        // Keep error log for connection check
        print('Connection check error: $e');
        return false;
      }
    });
  }

  Future<void> getDeviceTimeV2() {
    return _enqueue(() async {
      try {
        final result = await _platform.invokeMethod('getDeviceTimeV2');
        await _firestoreService.uploadHealthDataV2(
            type: "device_time", data: result);
      } on PlatformException catch (e) {
        // Keep error log for device time
        print('Device time error: ${e.message}');
        rethrow;
      }
    });
  }

  Future<void> getDeviceMacV2() {
    return _enqueue(() async {
      try {
        final result = await _platform.invokeMethod('getDeviceMacV2');
        if (result != null && result != "") {
          globalController.macId.value = result.toString();
        }
      } on PlatformException catch (e) {
        // Keep error log for MAC address
        print('MAC error: ${e.message}');
      }
    });
  }

  Future<int> getBatteryLevelV2() {
    return _enqueue(() async {
      try {
        final result = await _platform.invokeMethod('handleGetBatteryLevelV2');
        if (result != null && result != "") {
          int power = int.parse(result);
          globalController.ringBatteryPercentage.value = power;
          return power;
        }
        return 0;
      } on PlatformException catch (e) {
        // Keep error log for battery level
        print('Battery error: ${e.message}');
        return 0;
      }
    });
  }

  Future<void> getDeviceVersionV2() async {
    try {
      return _enqueue(() async {
        final result = await _platform.invokeMethod('getDeviceVersionV2');
        globalController.deviceVersion.value = result.toString();
      });
    } on PlatformException catch (e) {
      // Keep error log for device version
      print('Failed to get device version: ${e.message}');
      rethrow;
    }
  }

  Future<void> getBloodOxygenV2() async {
    try {
      return _enqueue(() async {
        final result = await _platform.invokeMethod('getBloodOxygenV2');
        List<BloodOxygenReadingModelV2> readings =
            BloodOxygenReadingModelV2.parseReadings(result);

        String uid = await _prefsService.getUid();
        if (uid != "") {
          bool isUpdated = await _firestoreService.uploadBatchDataV2(
              dataList: readings,
              uid: uid,
              collectionName: V2CollectionNames.saiwellRingBloodOxygenV2);

          modifyUpdatedFields(
            isUpdated: isUpdated,
            collection: V2CollectionNames.saiwellRingBloodOxygenV2,
          );

          await deleteUploadedFields(
            isUpdated: isUpdated,
            collection: V2CollectionNames.saiwellRingBloodOxygenV2,
          );
          _healthService.storeOxygenDataV2(readings);
        }
      });
    } on PlatformException catch (e) {
      // Keep error log for blood oxygen
      print('Failed to get blood oxygen: ${e.message}');
      rethrow;
    }
  }

  Future<List<StepsReadingModelV2>?> getTotalActivityDataWithModeV2({
    bool shouldUpdateInDB = true,
  }) async {
    try {
      return _enqueue(() async {
        final result =
            await _platform.invokeMethod('getTotalActivityDataWithModeV2');

        List<StepsReadingModelV2> readings =
            StepsReadingModelV2.parseReadings(result);

        if (shouldUpdateInDB) {
          String uid = await _prefsService.getUid();
          if (uid != "") {
            bool isUpdated = await _firestoreService.uploadBatchDataV2(
                dataList: readings,
                uid: uid,
                collectionName: V2CollectionNames.saiwellRingStepV2);

            modifyUpdatedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingStepV2,
            );

            await deleteUploadedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingStepV2,
            );

            _healthService.storeRingStepDataV2(readings);
          }
        }
        return readings;
      });
    } on PlatformException catch (e) {
      // Keep error log for activity data
      print('Failed to get activity data: ${e.message}');
      return null;
    }
  }

  Future<List<HRVReadingModelV2>?> getHRVDataV2({
    bool shouldUpdateInDB = true,
  }) async {
    try {
      return _enqueue(() async {
        final result = await _platform.invokeMethod('getHRVDataWithModeV2');
        List<HRVReadingModelV2> readings =
            HRVReadingModelV2.parseReadings(result);
        if (shouldUpdateInDB) {
          String uid = await _prefsService.getUid();
          if (uid != "") {
            bool isUpdated = await _firestoreService.uploadBatchDataV2(
                dataList: readings,
                uid: uid,
                collectionName: V2CollectionNames.saiwellRingHrvV2);

            modifyUpdatedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingHrvV2,
            );

            await deleteUploadedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingHrvV2,
            );
          }

          _healthService.storeHrvDataV2(readings);
        }
        return readings;
      });
    } on PlatformException catch (e) {
      // Keep error log for HRV data
      print('Failed to get HRV data: ${e.message}');
      return null;
    }
  }

  Future<List<SleepReadingModelV2>?> getSleepDataV2({
    bool shouldUpdateInDB = true,
  }) async {
    try {
      return _enqueue(() async {
        final result = await _platform.invokeMethod('getSleepDataV2');
        List<SleepReadingModelV2> readings =
            SleepReadingModelV2.parseReadings(result);
        if (shouldUpdateInDB) {
          String uid = await _prefsService.getUid();
          if (uid != "") {
            bool isUpdated = await _firestoreService.uploadBatchDataV2(
                dataList: readings,
                uid: uid,
                collectionName: V2CollectionNames.saiwellRingSleepV2);

            modifyUpdatedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingSleepV2,
            );

            await deleteUploadedFields(
              isUpdated: isUpdated,
              collection: V2CollectionNames.saiwellRingSleepV2,
            );
          }
          _healthService.storeSleepDataV2(readings);
        }
        return readings;
      });
    } on PlatformException catch (e) {
      // Keep error log for sleep data
      print('Failed to get sleep data: ${e.message}');
      return null;
    }
  }

  Future<void> getTemperatureDataV2() async {
    try {
      return _enqueue(() async {
        final result = await _platform.invokeMethod('getTemperatureDataV2');
        List<TemperatureReadingModelV2> readings =
            TemperatureReadingModelV2.parseReadings(result);
        String uid = await _prefsService.getUid();
        if (uid != "") {
          bool isUpdated = await _firestoreService.uploadBatchDataV2(
              dataList: readings,
              uid: uid,
              collectionName: V2CollectionNames.saiwellRingTemperatureV2);

          modifyUpdatedFields(
            isUpdated: isUpdated,
            collection: V2CollectionNames.saiwellRingTemperatureV2,
          );

          await deleteUploadedFields(
            isUpdated: isUpdated,
            collection: V2CollectionNames.saiwellRingTemperatureV2,
          );
        }
      });
    } on PlatformException catch (e) {
      // Keep error log for temperature data
      print('Failed to get temperature data: ${e.message}');
      rethrow;
    }
  }

  Future<void> postDateTimeV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('postDateTimeV2');
      });
    } on PlatformException catch (e) {
      // Keep error log for post date time
      print('Failed to post date time: ${e.message}');
      rethrow;
    }
  }

  Future<void> deleteHRVDataV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('deleteHRVDataV2');
      });
    } catch (e) {
      // Keep error log for delete HRV data
      print("Failed to delete deleteHRVDataV2$e");
    }
  }

  Future<void> deleteTemperatureDataV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('deleteTemperatureDataV2');
      });
    } catch (e) {
      // Keep error log for delete temperature data
      print("Failed to delete deleteTemperatureDataV2$e");
    }
  }

  Future<void> deleteSleepDataV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('deleteSleepDataV2');
      });
    } catch (e) {
      // Keep error log for delete sleep data
      print("Failed to delete deleteSleepDataV2$e");
    }
  }

  Future<void> deleteBloodOxygenV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('deleteBloodOxygenV2');
      });
    } catch (e) {
      // Keep error log for delete blood oxygen data
      print("Failed to delete deleteBloodOxygenV2$e");
    }
  }

  Future<void> deleteTotalActivityDataV2() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('deleteTotalActivityDataV2');
      });
    } catch (e) {
      // Keep error log for delete activity data
      print("Failed to delete deleteTotalActivityDataV2$e");
    }
  }

  Future<void> setAutomaticHRMonitoringOneV2() async {
    try {
      return _enqueue(() async {
        HRMonitoringConfigRemoteConfigModel?
            hrMonitoringConfigRemoteConfigModel =
            _firebaseRemoteConfigService.getHRMonitoringConfigByType(2);
        await _platform.invokeMethod('setAutomaticHRMonitoringOneV2', {
          'startHour': hrMonitoringConfigRemoteConfigModel?.startHour,
          'startMinute': hrMonitoringConfigRemoteConfigModel?.startMinute,
          'endHour': hrMonitoringConfigRemoteConfigModel?.endHour,
          'endMinute': hrMonitoringConfigRemoteConfigModel?.endMinute,
          'weeks': hrMonitoringConfigRemoteConfigModel?.weeks,
          'intervalTime': hrMonitoringConfigRemoteConfigModel?.intervalTime,
        });
      });
    } on PlatformException catch (e) {
      // Keep error log for HR monitoring
      print('Failed to set HR monitoring one: ${e.message}');
    }
  }

  Future<void> setAutomaticHRMonitoringTwoV2() async {
    try {
      return _enqueue(() async {
        HRMonitoringConfigRemoteConfigModel?
            hrMonitoringConfigRemoteConfigModel =
            _firebaseRemoteConfigService.getHRMonitoringConfigByType(3);
        await _platform.invokeMethod('setAutomaticHRMonitoringTwoV2', {
          'startHour': hrMonitoringConfigRemoteConfigModel?.startHour,
          'startMinute': hrMonitoringConfigRemoteConfigModel?.startMinute,
          'endHour': hrMonitoringConfigRemoteConfigModel?.endHour,
          'endMinute': hrMonitoringConfigRemoteConfigModel?.endMinute,
          'weeks': hrMonitoringConfigRemoteConfigModel?.weeks,
          'intervalTime': hrMonitoringConfigRemoteConfigModel?.intervalTime,
        });
      });
    } on PlatformException catch (e) {
      // Keep error log for HR monitoring
      print('Failed to set HR monitoring two: ${e.message}');
    }
  }

  Future<void> setAutomaticHRMonitoringThreeV2() async {
    try {
      return _enqueue(() async {
        HRMonitoringConfigRemoteConfigModel?
            hrMonitoringConfigRemoteConfigModel =
            _firebaseRemoteConfigService.getHRMonitoringConfigByType(4);
        await _platform.invokeMethod('setAutomaticHRMonitoringThreeV2', {
          'startHour': hrMonitoringConfigRemoteConfigModel?.startHour,
          'startMinute': hrMonitoringConfigRemoteConfigModel?.startMinute,
          'endHour': hrMonitoringConfigRemoteConfigModel?.endHour,
          'endMinute': hrMonitoringConfigRemoteConfigModel?.endMinute,
          'weeks': hrMonitoringConfigRemoteConfigModel?.weeks,
          'intervalTime': hrMonitoringConfigRemoteConfigModel?.intervalTime,
        });
      });
    } on PlatformException catch (e) {
      // Keep error log for HR monitoring
      print('Failed to set HR monitoring three: ${e.message}');
    }
  }

  Future<List> getHealthData(HealthDataType type) async {
    try {
      return _enqueue(() async {
        final result = await _platform
            .invokeMethod('getHealthData', {'type': type.getType()});
        return result ?? [];
      });
    } on PlatformException catch (e) {
      // Keep error log for health data
      print('Failed to get health data: ${e.message}');
      return [];
    }
  }

  Future<void> resetDataQueue() async {
    try {
      return _enqueue(() async {
        if (Platform.isAndroid) {
          await _platform.invokeMethod('resetDataQueue');
        }
      });
    } on PlatformException catch (e) {
      // Keep error log for reset data queue
      print('Failed to reset data queue: ${e.message}');
    }
  }

  Future<void> deleteHealthData(HealthDataType type) async {
    try {
      return _enqueue(() async {
        await _platform
            .invokeMethod('deleteHealthData', {'type': type.getType()});
      });
    } on PlatformException catch (e) {
      // Keep error log for delete health data
      print('Failed to delete health data: ${e.message}');
    }
  }

  // Future<dynamic> getDeviceInfo() async {
  //   try {
  //
  //     final result = await _platform.invokeMethod('getDeviceInfo');
  //     print('--------Native getDeviceInfo response: $result');
  //     return result;
  //   } on PlatformException catch (e) {
  //     print('Failed to get device info: ${e.message}');
  //     return {};
  //   }
  // }

  // Future<String> getBaseURL() async {
  //   try {
  //     return _enqueue(() async {
  //       final result = await _platform.invokeMethod('getBaseURL');
  //       print('--------Native getBaseURL response: $result');
  //       return result ?? "";
  //     });
  //   } on PlatformException catch (e) {
  //     print('Failed to get base URL: ${e.message}');
  //     return "";
  //   }
  // }

  Future<void> requestHealthKitPermissions() async {
    try {
      return _enqueue(() async {
        await _platform.invokeMethod('requestHealthKitPermissions');
      });
    } catch (e) {
      // Keep error log for HealthKit permissions
      print('Error requesting HealthKit permissions: $e');
      rethrow;
    }
  }

  Future<dynamic> getLastHealthKitUpdate() async {
    try {
      return _enqueue(() async {
        return await _platform.invokeMethod('getLastHealthKitUpdate');
      });
    } catch (e) {
      // Keep error log for HealthKit update
      print('Error getting last HealthKit update: $e');
      return null;
    }
  }

  //bg Data
  void initializeBgServiceForApple() {
    if (_isInitialized) return;
    _isInitialized = true;

    healthDataStream.listen((data) {
      _processHealthData(data);
    });

    _processCachedHealthData();
  }

  Future<void> _processCachedHealthData() async {
    try {
      final result = await getLastHealthKitUpdate();
      if (result != null) {
        await _processHealthData(result);
      }
    } catch (e) {
      // Keep error log for cached health data processing
      print('Error processing cached health data: $e');
    }
  }

  Future<void> _processHealthData(dynamic rawData) async {
    try {
      Map<String, dynamic> convertMap(Map<dynamic, dynamic> map) {
        return map.map((key, value) {
          if (value is Map) {
            return MapEntry(key.toString(), convertMap(value));
          }
          if (value is List) {
            return MapEntry(key.toString(),
                value.map((e) => e is Map ? convertMap(e) : e).toList());
          }
          return MapEntry(key.toString(), value);
        });
      }

      final rawHealthData = convertMap(rawData as Map<dynamic, dynamic>);

      _healthData = rawHealthData;

      if (_healthData.isNotEmpty) {
        String uid = await prefsService.getUid();

        if (uid.isEmpty) {
          return;
        }

        DocumentReference mainUserDocRef =
            firestore.collection("AppleHealthData").doc(uid);
        Set<String> processedKeys = {};
        Map<String, int> uploadCountByType = {};

        for (String dataKey in _healthData.keys) {
          String? convertedKey = healthKitTypeMap[dataKey];

          if (convertedKey != null) {
            Map<String, dynamic> dataValue = _healthData[dataKey];
            List<dynamic> dataPoints = dataValue['dataPoints'] ?? [];

            if (dataPoints.isNotEmpty) {
              int uploadedCount = 0;
              for (var dataPoint in dataPoints) {
                // Skip uploading from our source but don't add to processedKeys
                if (dataPoint['sourceId'] == 'com.saigeware.sh' ||
                    dataPoint['sourceId'] == 'com.saiwell.sw') {
                  continue;
                }

                await _uploadToFireStore(uid, convertedKey, dataPoint);
                uploadedCount++;
              }

              // Only add to processedKeys if we actually uploaded data for this type
              if (uploadedCount > 0) {
                processedKeys.add(convertedKey);
                uploadCountByType[convertedKey] = uploadedCount;
              }
            }
          }
        }

        // Update lastUpdated timestamps only for types that had data uploaded
        if (processedKeys.isNotEmpty) {
          Map<String, dynamic> updateData = {
            'lastUpdated': FieldValue.serverTimestamp(),
          };

          // Add individual source lastUpdated timestamps only for types that had uploads
          for (String key in processedKeys) {
            updateData['${key}_LastUpdated'] = FieldValue.serverTimestamp();
          }
          await mainUserDocRef.set(updateData, SetOptions(merge: true));
        }
      }
    } catch (e, stackTrace) {
      // Keep error log for health data processing
      debugPrint('[_processHealthData] Error processing health data: $e');
      debugPrint('[_processHealthData] Stack trace: $stackTrace');
    }
  }

  Future<void> _uploadToFireStore(
      String uid, String convertedKey, Map<String, dynamic> dataPoint) async {
    try {
      DocumentReference mainUserDocRef =
          firestore.collection("AppleHealthData").doc(uid);

      DocumentReference userDocRef = mainUserDocRef
          .collection(convertedKey)
          .doc('${dataPoint['startTimestamp']}');

      DocumentSnapshot existingDoc = await userDocRef.get();

      if (!existingDoc.exists) {
        int timeInMilliseconds = dataPoint['endTimestamp'] * 1000;
        Timestamp vitalCollectedTimestamp =
            Timestamp.fromMillisecondsSinceEpoch(timeInMilliseconds);

        dataPoint["unit"] =
            unitHealthKitTypeMap[dataPoint["unit"]] ?? dataPoint["unit"];

        Map<String, dynamic> firestoreData = {
          ...dataPoint,
          'created': FieldValue.serverTimestamp(),
          'vitalCollectedTimestamp': vitalCollectedTimestamp,
        };

        await userDocRef.set(firestoreData, SetOptions(merge: true));
      }
    } catch (e) {
      // Keep error log for Firestore upload
      debugPrint(
          '[_uploadToFirestore] Error pushing health data to Firestore: $e');
    }
  }
}

class Operation {
  final String name;
  final Future<void> Function() function;

  Operation(this.name, this.function);
}
