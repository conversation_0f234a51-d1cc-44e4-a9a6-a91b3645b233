name: SAiWELL
description: A new Flutter project.

publish_to: "none"
version: 3.6.0+2025073013
environment:
  sdk: ">=3.0.0 <4.0.0"
  
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_inappwebview: ^6.1.5
  get: ^4.6.5
  shimmer: ^3.0.0
  cached_network_image: ^3.4.0
  network_info_plus: ^6.0.1
  flutter_svg: ^2.0.16
  flutter_spinkit: ^5.2.1
  location: ^7.0.1
  http: ^1.2.2
  synchronized: ^3.3.1
  firebase_core: ^3.10.0
  firebase_messaging: 15.2.0
  flutter_local_notifications: ^17.2.2
  permission_handler: ^11.3.1
  cloud_firestore: 5.4.4
  shared_preferences: ^2.2.3
  connectivity_plus: ^6.1.0
  health: ^12.2.0
  firebase_remote_config: 5.3.0
  package_info_plus: ^8.0.0
  open_store: ^0.5.0
  intl: 0.19.0
  path_provider: ^2.1.5
  share_plus: ^7.2.1
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0
  flutter_reactive_ble: ^5.3.1
  fluttertoast: ^8.2.12
  record: ^5.2.0
  audio_waveforms: ^1.0.0
  path: ^1.9.0
  firebase_crashlytics: ^4.3.0
  firebase_analytics: ^11.4.0
  device_info_plus: ^11.3.0
  mime_type: ^1.0.1
  url_launcher: ^6.3.1
  external_app_launcher: ^4.0.1
  flutter_native_splash: ^2.4.4
  camera: ^0.11.0+2
  arkit_plugin: ^1.1.2
  image: ^4.5.2
  firebase_auth: ^5.4.1
  ftoast: ^2.0.0
  syncfusion_flutter_charts: ^29.1.37+1
  native_device_orientation: ^1.2.1
  exif: ^3.3.0
  vector_math: ^2.1.4
  open_file_manager: ^2.0.1
  google_mlkit_face_mesh_detection: ^0.4.1
  google_mlkit_face_detection: ^0.13.1
  app_links: ^6.4.0
  app_settings: ^6.1.1
  timezone: ^0.9.2
  workmanager: ^0.6.0
  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_logo_with_text.png"
  min_sdk_android: 21

flutter_native_splash:
  color: "#FFFFFF"
  android_12:
    color: "#FFFFFF"
  ios: true
  web: false

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/ble/
    - assets/icons/
    - assets/gCloud/credentials.json
